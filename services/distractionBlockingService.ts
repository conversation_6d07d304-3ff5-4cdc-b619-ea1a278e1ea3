import { Platform } from 'react-native';
import {
  BlockedApp,
  BlockedWebsite,
  DistractionAttempt,
  DistractionStats,
  BlockingSettings,
  BlockingSchedule,
} from '@/types/app';

const BLOCKED_APPS_KEY = 'isotope_blocked_apps';
const BLOCKED_WEBSITES_KEY = 'isotope_blocked_websites';
const DISTRACTION_ATTEMPTS_KEY = 'isotope_distraction_attempts';
const DISTRACTION_STATS_KEY = 'isotope_distraction_stats';
const BLOCKING_SETTINGS_KEY = 'isotope_blocking_settings';

const DEFAULT_BLOCKING_SETTINGS: BlockingSettings = {
  isEnabled: true,
  blockDuringFocus: true,
  blockDuringBreaks: false,
  allowEmergencyAccess: true,
  emergencyAccessDuration: 5,
  showBlockingNotifications: true,
  strictMode: false,
  whitelistedApps: [],
  whitelistedWebsites: [],
};

const DEFAULT_BLOCKED_APPS: BlockedApp[] = [
  {
    id: 'instagram',
    name: 'Instagram',
    packageName: 'com.instagram.android',
    icon: '📷',
    category: 'social',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    packageName: 'com.zhiliaoapp.musically',
    icon: '🎵',
    category: 'entertainment',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
  {
    id: 'youtube',
    name: 'YouTube',
    packageName: 'com.google.android.youtube',
    icon: '📺',
    category: 'entertainment',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
  {
    id: 'twitter',
    name: 'Twitter/X',
    packageName: 'com.twitter.android',
    icon: '🐦',
    category: 'social',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
  {
    id: 'facebook',
    name: 'Facebook',
    packageName: 'com.facebook.katana',
    icon: '📘',
    category: 'social',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    packageName: 'com.whatsapp',
    icon: '💬',
    category: 'social',
    isBlocked: false,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
];

const DEFAULT_BLOCKED_WEBSITES: BlockedWebsite[] = [
  {
    id: 'instagram-web',
    name: 'Instagram',
    url: 'https://instagram.com',
    domain: 'instagram.com',
    category: 'social',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
  {
    id: 'youtube-web',
    name: 'YouTube',
    url: 'https://youtube.com',
    domain: 'youtube.com',
    category: 'entertainment',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
  {
    id: 'twitter-web',
    name: 'Twitter/X',
    url: 'https://twitter.com',
    domain: 'twitter.com',
    category: 'social',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
  {
    id: 'facebook-web',
    name: 'Facebook',
    url: 'https://facebook.com',
    domain: 'facebook.com',
    category: 'social',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
  {
    id: 'reddit-web',
    name: 'Reddit',
    url: 'https://reddit.com',
    domain: 'reddit.com',
    category: 'social',
    isBlocked: true,
    blockDuringFocus: true,
    blockDuringBreaks: false,
  },
];

class DistractionBlockingService {
  private blockedApps: BlockedApp[] = [];
  private blockedWebsites: BlockedWebsite[] = [];
  private distractionAttempts: DistractionAttempt[] = [];
  private distractionStats: DistractionStats | null = null;
  private blockingSettings: BlockingSettings = DEFAULT_BLOCKING_SETTINGS;
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) return;
    
    await this.loadData();
    this.isInitialized = true;
  }

  private async loadData() {
    try {
      if (Platform.OS === 'web') {
        // Load blocked apps
        const appsData = localStorage.getItem(BLOCKED_APPS_KEY);
        this.blockedApps = appsData ? JSON.parse(appsData) : DEFAULT_BLOCKED_APPS;

        // Load blocked websites
        const websitesData = localStorage.getItem(BLOCKED_WEBSITES_KEY);
        this.blockedWebsites = websitesData ? JSON.parse(websitesData) : DEFAULT_BLOCKED_WEBSITES;

        // Load distraction attempts
        const attemptsData = localStorage.getItem(DISTRACTION_ATTEMPTS_KEY);
        if (attemptsData) {
          const parsed = JSON.parse(attemptsData);
          this.distractionAttempts = parsed.map((attempt: any) => ({
            ...attempt,
            timestamp: new Date(attempt.timestamp),
          }));
        }

        // Load distraction stats
        const statsData = localStorage.getItem(DISTRACTION_STATS_KEY);
        if (statsData) {
          const parsed = JSON.parse(statsData);
          this.distractionStats = {
            ...parsed,
            lastUpdated: new Date(parsed.lastUpdated),
          };
        }

        // Load blocking settings
        const settingsData = localStorage.getItem(BLOCKING_SETTINGS_KEY);
        this.blockingSettings = settingsData ? JSON.parse(settingsData) : DEFAULT_BLOCKING_SETTINGS;
      }
    } catch (error) {
      console.error('Error loading distraction blocking data:', error);
    }
  }

  private async saveData() {
    try {
      if (Platform.OS === 'web') {
        localStorage.setItem(BLOCKED_APPS_KEY, JSON.stringify(this.blockedApps));
        localStorage.setItem(BLOCKED_WEBSITES_KEY, JSON.stringify(this.blockedWebsites));
        localStorage.setItem(DISTRACTION_ATTEMPTS_KEY, JSON.stringify(this.distractionAttempts));
        if (this.distractionStats) {
          localStorage.setItem(DISTRACTION_STATS_KEY, JSON.stringify(this.distractionStats));
        }
        localStorage.setItem(BLOCKING_SETTINGS_KEY, JSON.stringify(this.blockingSettings));
      }
    } catch (error) {
      console.error('Error saving distraction blocking data:', error);
    }
  }

  // Getters
  getBlockedApps(): BlockedApp[] {
    return this.blockedApps;
  }

  getBlockedWebsites(): BlockedWebsite[] {
    return this.blockedWebsites;
  }

  getDistractionAttempts(): DistractionAttempt[] {
    return this.distractionAttempts;
  }

  getDistractionStats(): DistractionStats | null {
    return this.distractionStats;
  }

  getBlockingSettings(): BlockingSettings {
    return this.blockingSettings;
  }

  // App management
  async addBlockedApp(app: Omit<BlockedApp, 'id'>): Promise<BlockedApp> {
    const newApp: BlockedApp = {
      ...app,
      id: Date.now().toString(),
    };
    this.blockedApps.push(newApp);
    await this.saveData();
    return newApp;
  }

  async updateBlockedApp(id: string, updates: Partial<BlockedApp>): Promise<void> {
    const index = this.blockedApps.findIndex(app => app.id === id);
    if (index !== -1) {
      this.blockedApps[index] = { ...this.blockedApps[index], ...updates };
      await this.saveData();
    }
  }

  async removeBlockedApp(id: string): Promise<void> {
    this.blockedApps = this.blockedApps.filter(app => app.id !== id);
    await this.saveData();
  }

  // Website management
  async addBlockedWebsite(website: Omit<BlockedWebsite, 'id'>): Promise<BlockedWebsite> {
    const newWebsite: BlockedWebsite = {
      ...website,
      id: Date.now().toString(),
    };
    this.blockedWebsites.push(newWebsite);
    await this.saveData();
    return newWebsite;
  }

  async updateBlockedWebsite(id: string, updates: Partial<BlockedWebsite>): Promise<void> {
    const index = this.blockedWebsites.findIndex(website => website.id === id);
    if (index !== -1) {
      this.blockedWebsites[index] = { ...this.blockedWebsites[index], ...updates };
      await this.saveData();
    }
  }

  async removeBlockedWebsite(id: string): Promise<void> {
    this.blockedWebsites = this.blockedWebsites.filter(website => website.id !== id);
    await this.saveData();
  }

  // Settings management
  async updateBlockingSettings(updates: Partial<BlockingSettings>): Promise<void> {
    this.blockingSettings = { ...this.blockingSettings, ...updates };
    await this.saveData();
  }

  // Core blocking logic
  isAppBlocked(appId: string, context: 'focus' | 'break' | 'normal'): boolean {
    if (!this.blockingSettings.isEnabled) return false;

    const app = this.blockedApps.find(a => a.id === appId);
    if (!app || !app.isBlocked) return false;

    // Check whitelist
    if (this.blockingSettings.whitelistedApps.includes(appId)) return false;

    // Check context-specific blocking
    if (context === 'focus' && !app.blockDuringFocus) return false;
    if (context === 'break' && !app.blockDuringBreaks) return false;

    // Check custom schedule
    if (app.customSchedule && app.customSchedule.length > 0) {
      return this.isCurrentTimeInSchedule(app.customSchedule);
    }

    return true;
  }

  isWebsiteBlocked(websiteId: string, context: 'focus' | 'break' | 'normal'): boolean {
    if (!this.blockingSettings.isEnabled) return false;

    const website = this.blockedWebsites.find(w => w.id === websiteId);
    if (!website || !website.isBlocked) return false;

    // Check whitelist
    if (this.blockingSettings.whitelistedWebsites.includes(websiteId)) return false;

    // Check context-specific blocking
    if (context === 'focus' && !website.blockDuringFocus) return false;
    if (context === 'break' && !website.blockDuringBreaks) return false;

    // Check custom schedule
    if (website.customSchedule && website.customSchedule.length > 0) {
      return this.isCurrentTimeInSchedule(website.customSchedule);
    }

    return true;
  }

  isUrlBlocked(url: string, context: 'focus' | 'break' | 'normal'): boolean {
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      const website = this.blockedWebsites.find(w =>
        w.domain === domain || w.url === url
      );

      if (website) {
        return this.isWebsiteBlocked(website.id, context);
      }

      return false;
    } catch {
      return false;
    }
  }

  private isCurrentTimeInSchedule(schedules: BlockingSchedule[]): boolean {
    const now = new Date();
    const currentDay = now.getDay();
    const currentTime = now.getHours() * 60 + now.getMinutes();

    return schedules.some(schedule => {
      if (!schedule.isActive) return false;
      if (!schedule.daysOfWeek.includes(currentDay)) return false;

      const [startHour, startMin] = schedule.startTime.split(':').map(Number);
      const [endHour, endMin] = schedule.endTime.split(':').map(Number);
      const startTime = startHour * 60 + startMin;
      const endTime = endHour * 60 + endMin;

      if (startTime <= endTime) {
        return currentTime >= startTime && currentTime <= endTime;
      } else {
        // Handle overnight schedules
        return currentTime >= startTime || currentTime <= endTime;
      }
    });
  }

  // Attempt tracking
  async recordDistractionAttempt(
    appName: string,
    context: 'focus' | 'break' | 'normal',
    sessionId?: string,
    appId?: string,
    websiteId?: string
  ): Promise<DistractionAttempt> {
    const wasBlocked = appId ?
      this.isAppBlocked(appId, context) :
      websiteId ? this.isWebsiteBlocked(websiteId, context) : false;

    const blockReason = context === 'focus' ? 'focus_session' :
                       context === 'break' ? 'break_time' : 'manual_block';

    const attempt: DistractionAttempt = {
      id: Date.now().toString(),
      appId,
      websiteId,
      appName,
      timestamp: new Date(),
      sessionId,
      wasBlocked,
      blockReason,
    };

    this.distractionAttempts.push(attempt);
    await this.updateStats();
    await this.saveData();

    return attempt;
  }

  private async updateStats(): Promise<void> {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todaysAttempts = this.distractionAttempts.filter(
      attempt => attempt.timestamp >= today
    );

    const totalAttempts = todaysAttempts.length;
    const blockedAttempts = todaysAttempts.filter(a => a.wasBlocked).length;
    const allowedAttempts = totalAttempts - blockedAttempts;

    // Find most blocked app/website
    const appCounts = new Map<string, number>();
    const websiteCounts = new Map<string, number>();

    todaysAttempts.filter(a => a.wasBlocked).forEach(attempt => {
      if (attempt.appId) {
        appCounts.set(attempt.appName, (appCounts.get(attempt.appName) || 0) + 1);
      }
      if (attempt.websiteId) {
        websiteCounts.set(attempt.appName, (websiteCounts.get(attempt.appName) || 0) + 1);
      }
    });

    const mostBlockedApp = Array.from(appCounts.entries())
      .sort((a, b) => b[1] - a[1])[0]?.[0] || '';

    const mostBlockedWebsite = Array.from(websiteCounts.entries())
      .sort((a, b) => b[1] - a[1])[0]?.[0] || '';

    this.distractionStats = {
      totalAttempts,
      blockedAttempts,
      allowedAttempts,
      mostBlockedApp,
      mostBlockedWebsite,
      totalTimeBlocked: blockedAttempts * 30, // Estimate 30 seconds per blocked attempt
      streakDays: this.calculateStreakDays(),
      lastUpdated: new Date(),
    };
  }

  private calculateStreakDays(): number {
    // Calculate consecutive days with successful blocking
    const days = new Map<string, boolean>();
    const now = new Date();

    // Check last 30 days
    for (let i = 0; i < 30; i++) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateKey = date.toDateString();

      const dayAttempts = this.distractionAttempts.filter(attempt =>
        attempt.timestamp.toDateString() === dateKey
      );

      // Consider it a successful day if there were attempts and most were blocked
      const successfulDay = dayAttempts.length === 0 ||
        (dayAttempts.filter(a => a.wasBlocked).length / dayAttempts.length) >= 0.8;

      days.set(dateKey, successfulDay);
    }

    // Count consecutive successful days from today
    let streak = 0;
    for (let i = 0; i < 30; i++) {
      const date = new Date(now);
      date.setDate(date.getDate() - i);
      const dateKey = date.toDateString();

      if (days.get(dateKey)) {
        streak++;
      } else {
        break;
      }
    }

    return streak;
  }

  // Utility methods
  getTodaysAttempts(): DistractionAttempt[] {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    return this.distractionAttempts.filter(
      attempt => attempt.timestamp >= today
    );
  }

  getRecentlyBlockedApps(limit: number = 5): Array<{name: string, attempts: number}> {
    const recent = this.getTodaysAttempts()
      .filter(a => a.wasBlocked && a.appId)
      .reduce((acc, attempt) => {
        acc[attempt.appName] = (acc[attempt.appName] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    return Object.entries(recent)
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([name, attempts]) => ({ name, attempts }));
  }

  async clearAllData(): Promise<void> {
    this.blockedApps = [...DEFAULT_BLOCKED_APPS];
    this.blockedWebsites = [...DEFAULT_BLOCKED_WEBSITES];
    this.distractionAttempts = [];
    this.distractionStats = null;
    this.blockingSettings = { ...DEFAULT_BLOCKING_SETTINGS };
    await this.saveData();
  }
}

export const distractionBlockingService = new DistractionBlockingService();
