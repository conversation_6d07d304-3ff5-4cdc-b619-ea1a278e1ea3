/**
 * Icon Components using @expo/vector-icons
 * This replaces lucide-react-native with compatible Expo icons
 */

import React from 'react';
import {
  Ionicons,
  MaterialIcons,
  MaterialCommunityIcons,
  Feather,
  AntDesign,
  FontAwesome,
  FontAwesome5,
} from '@expo/vector-icons';

interface IconProps {
  size?: number;
  color?: string;
  style?: any;
}

// Timer and Time related icons
export const Play = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="play" size={size} color={color} {...props} />
);

export const Pause = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="pause" size={size} color={color} {...props} />
);

export const Square = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="stop" size={size} color={color} {...props} />
);

export const RotateCcw = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="refresh" size={size} color={color} {...props} />
);

export const Clock = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="time-outline" size={size} color={color} {...props} />
);

export const Timer = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <MaterialIcons name="timer" size={size} color={color} {...props} />
);

// Navigation and UI icons
export const Settings = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="settings-outline" size={size} color={color} {...props} />
);

export const Plus = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="add" size={size} color={color} {...props} />
);

export const X = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="close" size={size} color={color} {...props} />
);

export const ChartBar = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="bar-chart-outline" size={size} color={color} {...props} />
);

export const BarChart3 = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="bar-chart-outline" size={size} color={color} {...props} />
);

export const Target = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <MaterialIcons name="gps-fixed" size={size} color={color} {...props} />
);

// Distraction blocking icons
export const Smartphone = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="phone-portrait-outline" size={size} color={color} {...props} />
);

export const Globe = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="globe-outline" size={size} color={color} {...props} />
);

export const Shield = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="shield-outline" size={size} color={color} {...props} />
);

export const Zap = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="flash" size={size} color={color} {...props} />
);

export const Coffee = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="cafe-outline" size={size} color={color} {...props} />
);

// Notification icons
export const CheckCircle = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="checkmark-circle" size={size} color={color} {...props} />
);

export const AlertTriangle = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="warning" size={size} color={color} {...props} />
);

// Subject picker icons
export const Palette = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="color-palette-outline" size={size} color={color} {...props} />
);

export const CreditCard = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="card-outline" size={size} color={color} {...props} />
);

export const Edit3 = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="create-outline" size={size} color={color} {...props} />
);

export const Trash2 = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="trash-outline" size={size} color={color} {...props} />
);

// Settings icons
export const Bell = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="notifications-outline" size={size} color={color} {...props} />
);

export const Info = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="information-circle-outline" size={size} color={color} {...props} />
);

export const Download = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="download-outline" size={size} color={color} {...props} />
);

export const Upload = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="cloud-upload-outline" size={size} color={color} {...props} />
);

// Additional commonly used icons
export const ArrowRight = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="arrow-forward" size={size} color={color} {...props} />
);

export const ArrowLeft = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="arrow-back" size={size} color={color} {...props} />
);

export const Home = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="home-outline" size={size} color={color} {...props} />
);

export const User = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="person-outline" size={size} color={color} {...props} />
);

export const Calendar = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="calendar-outline" size={size} color={color} {...props} />
);

export const Search = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="search-outline" size={size} color={color} {...props} />
);

export const Menu = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="menu-outline" size={size} color={color} {...props} />
);

export const More = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="ellipsis-horizontal" size={size} color={color} {...props} />
);

export const Eye = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="eye-outline" size={size} color={color} {...props} />
);

export const EyeOff = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="eye-off-outline" size={size} color={color} {...props} />
);

export const Lock = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="lock-closed-outline" size={size} color={color} {...props} />
);

export const Unlock = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="lock-open-outline" size={size} color={color} {...props} />
);

export const Star = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="star-outline" size={size} color={color} {...props} />
);

export const StarFilled = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="star" size={size} color={color} {...props} />
);

export const Heart = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="heart-outline" size={size} color={color} {...props} />
);

export const HeartFilled = ({ size = 24, color = '#000', ...props }: IconProps) => (
  <Ionicons name="heart" size={size} color={color} {...props} />
);

// Export all icons as a single object for easy importing
export const Icons = {
  Play,
  Pause,
  Square,
  RotateCcw,
  Clock,
  Timer,
  Settings,
  Plus,
  X,
  ChartBar,
  BarChart3,
  Target,
  Smartphone,
  Globe,
  Shield,
  Zap,
  Coffee,
  CheckCircle,
  AlertTriangle,
  Palette,
  CreditCard,
  Edit3,
  Trash2,
  Bell,
  Info,
  Download,
  Upload,
  ArrowRight,
  ArrowLeft,
  Home,
  User,
  Calendar,
  Search,
  Menu,
  More,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  Star,
  StarFilled,
  Heart,
  HeartFilled,
};

export default Icons;
