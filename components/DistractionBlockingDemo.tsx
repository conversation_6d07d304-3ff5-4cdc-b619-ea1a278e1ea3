import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Smartphone,
  Globe,
  Play,
  Zap,
  Coffee,
} from 'lucide-react-native';
import { useDistractionBlocking } from '@/hooks/useDistractionBlocking';
import { notificationService } from '@/services/notificationService';

interface DistractionBlockingDemoProps {
  isVisible: boolean;
}

export default function DistractionBlockingDemo({ isVisible }: DistractionBlockingDemoProps) {
  const {
    blockedApps,
    blockedWebsites,
    currentContext,
    isAppBlocked,
    isWebsiteBlocked,
    recordDistractionAttempt,
  } = useDistractionBlocking();

  if (!isVisible) {
    return null;
  }

  const handleAppAttempt = async (app: any) => {
    const blocked = isAppBlocked(app.id);
    
    // Record the attempt
    await recordDistractionAttempt(
      app.name,
      undefined, // sessionId
      app.id,
      undefined // websiteId
    );

    // Show notification
    if (blocked) {
      await notificationService.showBlockingNotification(app.name, 'focus_session');
    } else {
      await notificationService.showWarningNotification(
        `${app.name} access allowed - stay mindful! 🧘`
      );
    }
  };

  const handleWebsiteAttempt = async (website: any) => {
    const blocked = isWebsiteBlocked(website.id);
    
    // Record the attempt
    await recordDistractionAttempt(
      website.name,
      undefined, // sessionId
      undefined, // appId
      website.id
    );

    // Show notification
    if (blocked) {
      await notificationService.showBlockingNotification(website.name, 'focus_session');
    } else {
      await notificationService.showWarningNotification(
        `${website.name} access allowed - stay mindful! 🧘`
      );
    }
  };

  const handleTestNotifications = async () => {
    await notificationService.showSuccessNotification(
      '🎉 Focus session started! Distractions are now blocked.'
    );
    
    setTimeout(async () => {
      await notificationService.showWarningNotification(
        '⚠️ Remember to take breaks every 25 minutes!'
      );
    }, 2000);
  };

  const getContextColor = () => {
    switch (currentContext) {
      case 'focus':
        return '#6B46C1';
      case 'break':
        return '#10B981';
      default:
        return '#6B7280';
    }
  };

  const getContextIcon = () => {
    switch (currentContext) {
      case 'focus':
        return <Zap size={16} color="#FFFFFF" />;
      case 'break':
        return <Coffee size={16} color="#FFFFFF" />;
      default:
        return <Play size={16} color="#FFFFFF" />;
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Test Distraction Blocking</Text>
        <View style={[styles.contextBadge, { backgroundColor: getContextColor() }]}>
          {getContextIcon()}
          <Text style={styles.contextText}>
            {currentContext.charAt(0).toUpperCase() + currentContext.slice(1)}
          </Text>
        </View>
      </View>

      <TouchableOpacity style={styles.testButton} onPress={handleTestNotifications}>
        <LinearGradient
          colors={['#6B46C1', '#7C3AED']}
          style={styles.testGradient}
        >
          <Text style={styles.testButtonText}>Test Notifications</Text>
        </LinearGradient>
      </TouchableOpacity>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Test Apps */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Try Accessing Apps</Text>
          <View style={styles.grid}>
            {blockedApps.slice(0, 6).map((app) => {
              const blocked = isAppBlocked(app.id);
              return (
                <TouchableOpacity
                  key={app.id}
                  style={[
                    styles.appCard,
                    blocked && styles.blockedCard,
                  ]}
                  onPress={() => handleAppAttempt(app)}
                >
                  <Text style={styles.appIcon}>{app.icon}</Text>
                  <Text style={[
                    styles.appName,
                    blocked && styles.blockedText,
                  ]}>
                    {app.name}
                  </Text>
                  {blocked && (
                    <View style={styles.blockedBadge}>
                      <Smartphone size={12} color="#FFFFFF" />
                    </View>
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>

        {/* Test Websites */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Try Accessing Websites</Text>
          <View style={styles.grid}>
            {blockedWebsites.slice(0, 6).map((website) => {
              const blocked = isWebsiteBlocked(website.id);
              return (
                <TouchableOpacity
                  key={website.id}
                  style={[
                    styles.websiteCard,
                    blocked && styles.blockedCard,
                  ]}
                  onPress={() => handleWebsiteAttempt(website)}
                >
                  <Globe size={24} color={blocked ? "#EF4444" : "#6B7280"} />
                  <Text style={[
                    styles.websiteName,
                    blocked && styles.blockedText,
                  ]}>
                    {website.name}
                  </Text>
                  {blocked && (
                    <View style={styles.blockedBadge}>
                      <Globe size={12} color="#FFFFFF" />
                    </View>
                  )}
                </TouchableOpacity>
              );
            })}
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    margin: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
    maxHeight: 400,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  contextBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  contextText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  testButton: {
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  testGradient: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    alignItems: 'center',
  },
  testButtonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 12,
  },
  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  appCard: {
    width: '30%',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  websiteCard: {
    width: '30%',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  blockedCard: {
    backgroundColor: '#FEF2F2',
    borderColor: '#FECACA',
  },
  appIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  appName: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
    textAlign: 'center',
  },
  websiteName: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
    textAlign: 'center',
    marginTop: 4,
  },
  blockedText: {
    color: '#EF4444',
  },
  blockedBadge: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 20,
    height: 20,
    backgroundColor: '#EF4444',
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
