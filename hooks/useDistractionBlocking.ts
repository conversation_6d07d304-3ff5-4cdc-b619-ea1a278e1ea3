import { useState, useEffect, useCallback } from 'react';
import {
  BlockedApp,
  BlockedWebsite,
  DistractionAttempt,
  DistractionStats,
  BlockingSettings,
} from '@/types/app';
import { distractionBlockingService } from '@/services/distractionBlockingService';

export function useDistractionBlocking() {
  const [blockedApps, setBlockedApps] = useState<BlockedApp[]>([]);
  const [blockedWebsites, setBlockedWebsites] = useState<BlockedWebsite[]>([]);
  const [distractionAttempts, setDistractionAttempts] = useState<DistractionAttempt[]>([]);
  const [distractionStats, setDistractionStats] = useState<DistractionStats | null>(null);
  const [blockingSettings, setBlockingSettings] = useState<BlockingSettings | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [currentContext, setCurrentContext] = useState<'focus' | 'break' | 'normal'>('normal');

  // Initialize the service and load data
  useEffect(() => {
    const initializeService = async () => {
      try {
        await distractionBlockingService.initialize();
        await refreshData();
      } catch (error) {
        console.error('Error initializing distraction blocking service:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeService();
  }, []);

  const refreshData = useCallback(async () => {
    try {
      setBlockedApps(distractionBlockingService.getBlockedApps());
      setBlockedWebsites(distractionBlockingService.getBlockedWebsites());
      setDistractionAttempts(distractionBlockingService.getDistractionAttempts());
      setDistractionStats(distractionBlockingService.getDistractionStats());
      setBlockingSettings(distractionBlockingService.getBlockingSettings());
    } catch (error) {
      console.error('Error refreshing distraction blocking data:', error);
    }
  }, []);

  // App management functions
  const addBlockedApp = useCallback(async (app: Omit<BlockedApp, 'id'>) => {
    try {
      await distractionBlockingService.addBlockedApp(app);
      await refreshData();
    } catch (error) {
      console.error('Error adding blocked app:', error);
      throw error;
    }
  }, [refreshData]);

  const updateBlockedApp = useCallback(async (id: string, updates: Partial<BlockedApp>) => {
    try {
      await distractionBlockingService.updateBlockedApp(id, updates);
      await refreshData();
    } catch (error) {
      console.error('Error updating blocked app:', error);
      throw error;
    }
  }, [refreshData]);

  const removeBlockedApp = useCallback(async (id: string) => {
    try {
      await distractionBlockingService.removeBlockedApp(id);
      await refreshData();
    } catch (error) {
      console.error('Error removing blocked app:', error);
      throw error;
    }
  }, [refreshData]);

  // Website management functions
  const addBlockedWebsite = useCallback(async (website: Omit<BlockedWebsite, 'id'>) => {
    try {
      await distractionBlockingService.addBlockedWebsite(website);
      await refreshData();
    } catch (error) {
      console.error('Error adding blocked website:', error);
      throw error;
    }
  }, [refreshData]);

  const updateBlockedWebsite = useCallback(async (id: string, updates: Partial<BlockedWebsite>) => {
    try {
      await distractionBlockingService.updateBlockedWebsite(id, updates);
      await refreshData();
    } catch (error) {
      console.error('Error updating blocked website:', error);
      throw error;
    }
  }, [refreshData]);

  const removeBlockedWebsite = useCallback(async (id: string) => {
    try {
      await distractionBlockingService.removeBlockedWebsite(id);
      await refreshData();
    } catch (error) {
      console.error('Error removing blocked website:', error);
      throw error;
    }
  }, [refreshData]);

  // Settings management
  const updateBlockingSettings = useCallback(async (updates: Partial<BlockingSettings>) => {
    try {
      await distractionBlockingService.updateBlockingSettings(updates);
      await refreshData();
    } catch (error) {
      console.error('Error updating blocking settings:', error);
      throw error;
    }
  }, [refreshData]);

  // Blocking check functions
  const isAppBlocked = useCallback((appId: string, context?: 'focus' | 'break' | 'normal') => {
    return distractionBlockingService.isAppBlocked(appId, context || currentContext);
  }, [currentContext]);

  const isWebsiteBlocked = useCallback((websiteId: string, context?: 'focus' | 'break' | 'normal') => {
    return distractionBlockingService.isWebsiteBlocked(websiteId, context || currentContext);
  }, [currentContext]);

  const isUrlBlocked = useCallback((url: string, context?: 'focus' | 'break' | 'normal') => {
    return distractionBlockingService.isUrlBlocked(url, context || currentContext);
  }, [currentContext]);

  // Attempt recording
  const recordDistractionAttempt = useCallback(async (
    appName: string,
    sessionId?: string,
    appId?: string,
    websiteId?: string,
    context?: 'focus' | 'break' | 'normal'
  ) => {
    try {
      const attempt = await distractionBlockingService.recordDistractionAttempt(
        appName,
        context || currentContext,
        sessionId,
        appId,
        websiteId
      );
      await refreshData();
      return attempt;
    } catch (error) {
      console.error('Error recording distraction attempt:', error);
      throw error;
    }
  }, [currentContext, refreshData]);

  // Utility functions
  const getTodaysAttempts = useCallback(() => {
    return distractionBlockingService.getTodaysAttempts();
  }, []);

  const getRecentlyBlockedApps = useCallback((limit?: number) => {
    return distractionBlockingService.getRecentlyBlockedApps(limit);
  }, []);

  const clearAllData = useCallback(async () => {
    try {
      await distractionBlockingService.clearAllData();
      await refreshData();
    } catch (error) {
      console.error('Error clearing all data:', error);
      throw error;
    }
  }, [refreshData]);

  // Context management for focus sessions
  const setFocusContext = useCallback((context: 'focus' | 'break' | 'normal') => {
    setCurrentContext(context);
  }, []);

  // Computed values
  const isBlockingEnabled = blockingSettings?.isEnabled ?? false;
  const todaysAttempts = getTodaysAttempts();
  const todaysBlockedAttempts = todaysAttempts.filter(a => a.wasBlocked);
  const recentlyBlockedApps = getRecentlyBlockedApps(3);

  // Statistics
  const stats = {
    totalAppsBlocked: blockedApps.filter(app => app.isBlocked).length,
    totalWebsitesBlocked: blockedWebsites.filter(website => website.isBlocked).length,
    todaysAttempts: todaysAttempts.length,
    todaysBlockedAttempts: todaysBlockedAttempts.length,
    blockingEffectiveness: todaysAttempts.length > 0 ? 
      Math.round((todaysBlockedAttempts.length / todaysAttempts.length) * 100) : 100,
    streakDays: distractionStats?.streakDays ?? 0,
  };

  return {
    // Data
    blockedApps,
    blockedWebsites,
    distractionAttempts,
    distractionStats,
    blockingSettings,
    currentContext,
    isLoading,
    
    // App management
    addBlockedApp,
    updateBlockedApp,
    removeBlockedApp,
    
    // Website management
    addBlockedWebsite,
    updateBlockedWebsite,
    removeBlockedWebsite,
    
    // Settings
    updateBlockingSettings,
    
    // Blocking checks
    isAppBlocked,
    isWebsiteBlocked,
    isUrlBlocked,
    
    // Attempt tracking
    recordDistractionAttempt,
    
    // Utilities
    getTodaysAttempts,
    getRecentlyBlockedApps,
    clearAllData,
    refreshData,
    
    // Context management
    setFocusContext,
    
    // Computed values
    isBlockingEnabled,
    todaysAttempts,
    todaysBlockedAttempts,
    recentlyBlockedApps,
    stats,
  };
}
