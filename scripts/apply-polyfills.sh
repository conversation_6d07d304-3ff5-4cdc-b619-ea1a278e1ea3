#!/bin/bash

# Script to apply polyfills for problematic React Native packages
# This replaces native components with JavaScript-only implementations

echo "Applying polyfills for react-native-safe-area-context..."

# Copy polyfill files
cp polyfills/react-native-safe-area-context.js node_modules/react-native-safe-area-context/lib/module/polyfill.js
cp polyfills/react-native-safe-area-context.js node_modules/react-native-safe-area-context/lib/commonjs/polyfill.js

echo "Applying polyfills for react-native-screens..."

# Copy react-native-screens polyfill files
mkdir -p node_modules/react-native-screens/lib/module/fabric
mkdir -p node_modules/react-native-screens/lib/commonjs/fabric
cp polyfills/react-native-screens.js node_modules/react-native-screens/lib/module/polyfill.js
cp polyfills/react-native-screens.js node_modules/react-native-screens/lib/commonjs/polyfill.js
cp polyfills/react-native-screens-fabric.js node_modules/react-native-screens/lib/module/fabric/FullWindowOverlayNativeComponent.js
cp polyfills/react-native-screens-fabric.js node_modules/react-native-screens/lib/commonjs/fabric/FullWindowOverlayNativeComponent.js

# Replace native component with View
cat > node_modules/react-native-safe-area-context/lib/module/specs/NativeSafeAreaView.js << 'EOF'
// Polyfill: Replace native component with a simple View
import { View } from 'react-native';
export default View;
//# sourceMappingURL=NativeSafeAreaView.js.map
EOF

# Replace module index to use polyfill
cat > node_modules/react-native-safe-area-context/lib/module/index.js << 'EOF'
'use client';

// Polyfill: Use our custom implementation
export * from './polyfill.js';
//# sourceMappingURL=index.js.map
EOF

# Replace CommonJS index to use polyfill
cat > node_modules/react-native-safe-area-context/lib/commonjs/index.js << 'EOF'
"use strict";
'use client';

// Polyfill: Use our custom implementation
var polyfill = require("./polyfill.js");

Object.defineProperty(exports, "__esModule", {
  value: true
});

// Export all polyfill exports
Object.keys(polyfill).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return polyfill[key];
    }
  });
});

// Also export default
if (polyfill.default) {
  exports.default = polyfill.default;
}

//# sourceMappingURL=index.js.map
EOF

# Replace react-native-screens module index to use polyfill
cat > node_modules/react-native-screens/lib/module/index.js << 'EOF'
'use client';

// Polyfill: Use our custom implementation
export * from './polyfill.js';
//# sourceMappingURL=index.js.map
EOF

# Replace react-native-screens CommonJS index to use polyfill
cat > node_modules/react-native-screens/lib/commonjs/index.js << 'EOF'
"use strict";
'use client';

// Polyfill: Use our custom implementation
var polyfill = require("./polyfill.js");

Object.defineProperty(exports, "__esModule", {
  value: true
});

// Export all polyfill exports
Object.keys(polyfill).forEach(function (key) {
  if (key === "default" || key === "__esModule") return;
  Object.defineProperty(exports, key, {
    enumerable: true,
    get: function () {
      return polyfill[key];
    }
  });
});

// Also export default
if (polyfill.default) {
  exports.default = polyfill.default;
}

//# sourceMappingURL=index.js.map
EOF

# Note: SVG polyfills removed since we're using @expo/vector-icons instead

echo "Polyfills applied successfully!"
echo "Note: You'll need to run this script again after 'npm install' or 'npm ci'"
