const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Ensure TypeScript files are properly handled
config.resolver.sourceExts = [...config.resolver.sourceExts, 'ts', 'tsx'];

// Add resolver configuration for better module resolution
config.resolver.platforms = ['native', 'android', 'ios', 'web'];
config.resolver.resolverMainFields = ['browser', 'module', 'main'];
config.resolver.moduleFileExtensions = ['js', 'jsx', 'ts', 'tsx', 'json', 'cjs', 'mjs'];
config.resolver.unstable_enableSymlinks = false;

// Add alias to redirect problematic packages to our polyfills
config.resolver.alias = {
  ...config.resolver.alias,
  'react-native-safe-area-context': path.resolve(__dirname, 'polyfills/react-native-safe-area-context.js'),
  'react-native-screens': path.resolve(__dirname, 'polyfills/react-native-screens.js'),
  'react-native-screens/lib/module/fabric/FullWindowOverlayNativeComponent': path.resolve(__dirname, 'polyfills/react-native-screens-fabric.js'),
};

// Configure transformer for TypeScript
config.transformer = {
  ...config.transformer,
  unstable_allowRequireContext: true,
};

// Add watchman configuration
config.watchFolders = [__dirname];

module.exports = config;
