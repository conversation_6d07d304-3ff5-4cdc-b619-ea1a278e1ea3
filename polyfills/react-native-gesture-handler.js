/**
 * Polyfill for react-native-gesture-handler
 * This provides fallback implementations for web compatibility
 */

import React from 'react';
import { View, ScrollView, TouchableOpacity, PanResponder } from 'react-native';

// Gesture Handler Components
export const GestureHandlerRootView = ({ children, style, ...props }) => (
  <View style={[{ flex: 1 }, style]} {...props}>
    {children}
  </View>
);

export const PanGestureHandler = ({ children, onGestureEvent, onHandlerStateChange, ...props }) => {
  const panResponder = PanResponder.create({
    onMoveShouldSetPanResponder: () => true,
    onPanResponderMove: (evt, gestureState) => {
      if (onGestureEvent) {
        onGestureEvent({
          nativeEvent: {
            translationX: gestureState.dx,
            translationY: gestureState.dy,
            velocityX: gestureState.vx,
            velocityY: gestureState.vy,
          },
        });
      }
    },
    onPanResponderRelease: () => {
      if (onHandlerStateChange) {
        onHandlerStateChange({ nativeEvent: { state: State.END } });
      }
    },
  });

  return (
    <View {...panResponder.panHandlers} {...props}>
      {children}
    </View>
  );
};

export const TapGestureHandler = ({ children, onHandlerStateChange, ...props }) => (
  <TouchableOpacity
    onPress={() => {
      if (onHandlerStateChange) {
        onHandlerStateChange({ nativeEvent: { state: State.END } });
      }
    }}
    {...props}
  >
    {children}
  </TouchableOpacity>
);

export const LongPressGestureHandler = ({ children, onHandlerStateChange, ...props }) => (
  <TouchableOpacity
    onLongPress={() => {
      if (onHandlerStateChange) {
        onHandlerStateChange({ nativeEvent: { state: State.ACTIVE } });
      }
    }}
    {...props}
  >
    {children}
  </TouchableOpacity>
);

export const PinchGestureHandler = ({ children, ...props }) => (
  <View {...props}>
    {children}
  </View>
);

export const RotationGestureHandler = ({ children, ...props }) => (
  <View {...props}>
    {children}
  </View>
);

export const FlingGestureHandler = ({ children, ...props }) => (
  <View {...props}>
    {children}
  </View>
);

// Gesture Handler State Constants
export const State = {
  UNDETERMINED: 0,
  FAILED: 1,
  BEGAN: 2,
  CANCELLED: 3,
  ACTIVE: 4,
  END: 5,
};

// Gesture Handler Directions
export const Directions = {
  RIGHT: 1,
  LEFT: 2,
  UP: 4,
  DOWN: 8,
};

// Gesture Handler Utilities
export const gestureHandlerRootHOC = (Component) => Component;

// Swipeable component
export const Swipeable = ({ children, renderLeftActions, renderRightActions, ...props }) => (
  <View {...props}>
    {children}
  </View>
);

// DrawerLayout component
export const DrawerLayout = ({ children, ...props }) => (
  <View {...props}>
    {children}
  </View>
);

// Export all components and utilities
export default {
  GestureHandlerRootView,
  PanGestureHandler,
  TapGestureHandler,
  LongPressGestureHandler,
  PinchGestureHandler,
  RotationGestureHandler,
  FlingGestureHandler,
  State,
  Directions,
  gestureHandlerRootHOC,
  Swipeable,
  DrawerLayout,
};
