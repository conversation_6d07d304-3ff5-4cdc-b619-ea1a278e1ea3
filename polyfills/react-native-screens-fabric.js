/**
 * Polyfill for react-native-screens fabric components
 * This specifically handles the FullWindowOverlayNativeComponent that's causing issues
 */

import React from 'react';
import { View } from 'react-native';

// FullWindowOverlayNativeComponent - the specific component causing the error
const FullWindowOverlayNativeComponent = React.forwardRef((props, ref) => {
  const { children, style, ...otherProps } = props;
  
  return (
    <View 
      ref={ref}
      style={[
        {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 1000,
        },
        style
      ]} 
      {...otherProps}
    >
      {children}
    </View>
  );
});

FullWindowOverlayNativeComponent.displayName = 'FullWindowOverlayNativeComponent';

// Export the component
export default FullWindowOverlayNativeComponent;
export { FullWindowOverlayNativeComponent };
