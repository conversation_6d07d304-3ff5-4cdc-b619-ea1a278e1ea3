/**
 * Polyfill for react-native-reanimated
 * This provides fallback implementations for web compatibility
 */

import React from 'react';
import { View, Animated } from 'react-native';

// Animated View component
const AnimatedView = ({ children, style, ...props }) => (
  <View style={style} {...props}>
    {children}
  </View>
);

// Shared Values
export const useSharedValue = (initialValue) => {
  const ref = React.useRef({ value: initialValue });
  return ref.current;
};

export const useDerivedValue = (callback) => {
  const [value, setValue] = React.useState(callback());
  React.useEffect(() => {
    setValue(callback());
  }, [callback]);
  return { value };
};

// Animated Styles
export const useAnimatedStyle = (callback) => {
  return React.useMemo(() => callback(), [callback]);
};

// Animations
export const withTiming = (toValue, config = {}) => {
  return toValue;
};

export const withSpring = (toValue, config = {}) => {
  return toValue;
};

export const withDelay = (delay, animation) => {
  return animation;
};

export const withRepeat = (animation, numberOfReps = -1, reverse = false) => {
  return animation;
};

export const withSequence = (...animations) => {
  return animations[animations.length - 1];
};

// Gesture Events
export const useAnimatedGestureHandler = (handlers) => {
  return handlers;
};

// Scroll Events
export const useAnimatedScrollHandler = (handler) => {
  return handler;
};

// Layout Animations
export const Layout = {
  duration: (duration) => ({ duration }),
  delay: (delay) => ({ delay }),
  springify: () => ({}),
  damping: (damping) => ({ damping }),
  mass: (mass) => ({ mass }),
  stiffness: (stiffness) => ({ stiffness }),
  overshootClamping: (overshootClamping) => ({ overshootClamping }),
  restDisplacementThreshold: (restDisplacementThreshold) => ({ restDisplacementThreshold }),
  restSpeedThreshold: (restSpeedThreshold) => ({ restSpeedThreshold }),
};

export const FadeIn = Layout;
export const FadeOut = Layout;
export const SlideInLeft = Layout;
export const SlideInRight = Layout;
export const SlideInUp = Layout;
export const SlideInDown = Layout;
export const SlideOutLeft = Layout;
export const SlideOutRight = Layout;
export const SlideOutUp = Layout;
export const SlideOutDown = Layout;
export const ZoomIn = Layout;
export const ZoomOut = Layout;

// Worklets
export const runOnJS = (fn) => fn;
export const runOnUI = (fn) => fn;

// Interpolation
export const interpolate = (value, inputRange, outputRange, extrapolate) => {
  // Simple linear interpolation fallback
  if (inputRange.length !== outputRange.length) return outputRange[0];
  
  for (let i = 0; i < inputRange.length - 1; i++) {
    if (value >= inputRange[i] && value <= inputRange[i + 1]) {
      const ratio = (value - inputRange[i]) / (inputRange[i + 1] - inputRange[i]);
      return outputRange[i] + ratio * (outputRange[i + 1] - outputRange[i]);
    }
  }
  
  return outputRange[outputRange.length - 1];
};

export const interpolateColor = (value, inputRange, outputRange) => {
  return outputRange[0]; // Fallback to first color
};

// Easing functions
export const Easing = {
  linear: (t) => t,
  ease: (t) => t,
  quad: (t) => t * t,
  cubic: (t) => t * t * t,
  bezier: () => (t) => t,
  circle: (t) => 1 - Math.sqrt(1 - t * t),
  back: () => (t) => t,
  bounce: (t) => t,
  elastic: () => (t) => t,
  sin: (t) => Math.sin(t * Math.PI / 2),
  cos: (t) => 1 - Math.cos(t * Math.PI / 2),
  exp: (t) => Math.pow(2, 10 * (t - 1)),
  in: (easing) => easing,
  out: (easing) => (t) => 1 - easing(1 - t),
  inOut: (easing) => (t) => t < 0.5 ? easing(t * 2) / 2 : 1 - easing((1 - t) * 2) / 2,
};

// Default export
export default {
  View: AnimatedView,
  useSharedValue,
  useDerivedValue,
  useAnimatedStyle,
  useAnimatedGestureHandler,
  useAnimatedScrollHandler,
  withTiming,
  withSpring,
  withDelay,
  withRepeat,
  withSequence,
  interpolate,
  interpolateColor,
  runOnJS,
  runOnUI,
  Easing,
  Layout,
  FadeIn,
  FadeOut,
  SlideInLeft,
  SlideInRight,
  SlideInUp,
  SlideInDown,
  SlideOutLeft,
  SlideOutRight,
  SlideOutUp,
  SlideOutDown,
  ZoomIn,
  ZoomOut,
};

// Named exports for Animated components
export const Animated = {
  View: AnimatedView,
};
