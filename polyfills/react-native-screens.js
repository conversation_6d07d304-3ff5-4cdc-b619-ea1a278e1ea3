/**
 * Polyfill for react-native-screens
 * This replaces the problematic native components with pure JavaScript implementations
 * using React Native's built-in View components
 */

import React from 'react';
import { View, Platform } from 'react-native';

// Screen component - replaces the native Screen component
function Screen({ children, style, ...props }) {
  return (
    <View style={[{ flex: 1 }, style]} {...props}>
      {children}
    </View>
  );
}

// ScreenContainer component - replaces the native ScreenContainer component
function ScreenContainer({ children, style, ...props }) {
  return (
    <View style={[{ flex: 1 }, style]} {...props}>
      {children}
    </View>
  );
}

// ScreenStack component - replaces the native ScreenStack component
function ScreenStack({ children, style, ...props }) {
  return (
    <View style={[{ flex: 1 }, style]} {...props}>
      {children}
    </View>
  );
}

// ScreenStackHeaderConfig component - replaces the native header config
function ScreenStackHeaderConfig({ children, ...props }) {
  // For web/compatibility, we just return null as headers are handled by expo-router
  return null;
}

// ScreenStackHeaderSubview component
function ScreenStackHeaderSubview({ children, ...props }) {
  return null;
}

// FullWindowOverlay component - this is the one causing the error
function FullWindowOverlay({ children, style, ...props }) {
  return (
    <View 
      style={[
        {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 1000,
        },
        style
      ]} 
      {...props}
    >
      {children}
    </View>
  );
}

// Modal component replacement
function Modal({ children, style, ...props }) {
  return (
    <View 
      style={[
        {
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0,0,0,0.5)',
          zIndex: 999,
        },
        style
      ]} 
      {...props}
    >
      {children}
    </View>
  );
}

// SearchBar component (if used)
function SearchBar({ style, ...props }) {
  return <View style={[{ height: 44 }, style]} {...props} />;
}

// Constants that might be used
const ScreenOrientationLock = {
  DEFAULT: 'default',
  ALL: 'all',
  PORTRAIT: 'portrait',
  PORTRAIT_UP: 'portrait-up',
  PORTRAIT_DOWN: 'portrait-down',
  LANDSCAPE: 'landscape',
  LANDSCAPE_LEFT: 'landscape-left',
  LANDSCAPE_RIGHT: 'landscape-right',
};

const ScreenStackHeaderConfigBackButtonDisplayMode = {
  DEFAULT: 'default',
  GENERIC: 'generic',
  MINIMAL: 'minimal',
};

// Enable screens function - no-op for compatibility
function enableScreens(enabled = true) {
  // No-op for web/compatibility
  console.log('enableScreens called with:', enabled);
}

// Screen state constants
const ScreenState = {
  INACTIVE: 0,
  TRANSITIONING_OR_BELOW_TOP: 1,
  ON_TOP: 2,
};

// Export all the components and functions that react-native-screens provides
export {
  Screen,
  ScreenContainer,
  ScreenStack,
  ScreenStackHeaderConfig,
  ScreenStackHeaderSubview,
  FullWindowOverlay,
  Modal,
  SearchBar,
  enableScreens,
  ScreenOrientationLock,
  ScreenStackHeaderConfigBackButtonDisplayMode,
  ScreenState,
};

// Default export for compatibility
export default {
  Screen,
  ScreenContainer,
  ScreenStack,
  ScreenStackHeaderConfig,
  ScreenStackHeaderSubview,
  FullWindowOverlay,
  Modal,
  SearchBar,
  enableScreens,
  ScreenOrientationLock,
  ScreenStackHeaderConfigBackButtonDisplayMode,
  ScreenState,
};
