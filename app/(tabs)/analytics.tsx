import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { BarChart3, Calendar, Clock, Target } from '@/components/Icons';
import { Ionicons } from '@expo/vector-icons';
import IsotopeLogo from '@/components/IsotopeLogo';
import { useTimer } from '@/hooks/useTimer';
import { useSubjects } from '@/hooks/useSubjects';

const { width } = Dimensions.get('window');

export default function AnalyticsScreen() {
  const { sessions, getTotalTimeToday } = useTimer();
  const { subjects, getSubjectById } = useSubjects();
  const [selectedPeriod, setSelectedPeriod] = useState('week');

  const periods = ['week', 'month', 'year'];

  const getSessionsForPeriod = () => {
    const now = new Date();
    const startDate = new Date();
    
    switch (selectedPeriod) {
      case 'week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'year':
        startDate.setFullYear(now.getFullYear() - 1);
        break;
    }
    
    return sessions.filter(session => session.startTime >= startDate);
  };

  const getSubjectStats = () => {
    const periodSessions = getSessionsForPeriod();
    const subjectStats: { [key: string]: { name: string; time: number; color: string; sessions: number } } = {};
    
    periodSessions.forEach(session => {
      const subjectId = session.subjectId || 'none';
      const subject = subjectId !== 'none' ? getSubjectById(subjectId) : null;
      const name = subject?.name || 'No Subject';
      const color = subject?.color || '#9CA3AF';
      
      if (!subjectStats[subjectId]) {
        subjectStats[subjectId] = { name, time: 0, color, sessions: 0 };
      }
      
      subjectStats[subjectId].time += session.duration;
      subjectStats[subjectId].sessions += 1;
    });
    
    return Object.values(subjectStats).sort((a, b) => b.time - a.time);
  };

  const getDailyStats = () => {
    const dailyStats: { [key: string]: number } = {};
    const periodSessions = getSessionsForPeriod();
    
    periodSessions.forEach(session => {
      const dateKey = session.startTime.toDateString();
      if (!dailyStats[dateKey]) {
        dailyStats[dateKey] = 0;
      }
      dailyStats[dateKey] += session.duration;
    });
    
    return dailyStats;
  };

  const formatTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  const formatTimeShort = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h`;
    }
    return `${minutes}m`;
  };

  const subjectStats = getSubjectStats();
  const dailyStats = getDailyStats();
  const totalTime = getSessionsForPeriod().reduce((sum, session) => sum + session.duration, 0);
  const totalSessions = getSessionsForPeriod().length;
  const averageSession = totalSessions > 0 ? totalTime / totalSessions : 0;

  const renderBarChart = () => {
    const last7Days = Array.from({ length: 7 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (6 - i));
      return date;
    });

    const maxTime = Math.max(...last7Days.map(date => dailyStats[date.toDateString()] || 0));

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Daily Study Time</Text>
        <View style={styles.barChart}>
          {last7Days.map((date, index) => {
            const time = dailyStats[date.toDateString()] || 0;
            const height = maxTime > 0 ? (time / maxTime) * 120 : 0;
            
            return (
              <View key={index} style={styles.barContainer}>
                <View style={styles.barWrapper}>
                  <LinearGradient
                    colors={['#6366F1', '#8B5CF6']}
                    style={[styles.bar, { height: Math.max(height, 4) }]}
                  />
                </View>
                <Text style={styles.barLabel}>
                  {date.toLocaleDateString('en-US', { weekday: 'short' })}
                </Text>
                <Text style={styles.barValue}>{formatTimeShort(time)}</Text>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderSubjectChart = () => {
    const totalSubjectTime = subjectStats.reduce((sum, stat) => sum + stat.time, 0);

    return (
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>Subject Distribution</Text>
        {totalSubjectTime > 0 ? (
          <>
            <View style={styles.pieChart}>
              {subjectStats.map((stat, index) => {
                const percentage = (stat.time / totalSubjectTime) * 100;
                return (
                  <View
                    key={index}
                    style={[
                      styles.pieSlice,
                      {
                        backgroundColor: stat.color,
                        width: `${percentage}%`,
                      },
                    ]}
                  />
                );
              })}
            </View>
            <View style={styles.pieLegend}>
              {subjectStats.map((stat, index) => (
                <View key={index} style={styles.legendItem}>
                  <View style={[styles.legendColor, { backgroundColor: stat.color }]} />
                  <Text style={styles.legendText}>{stat.name}</Text>
                  <Text style={styles.legendValue}>{formatTime(stat.time)}</Text>
                </View>
              ))}
            </View>
          </>
        ) : (
          <View style={styles.emptyChart}>
            <BarChart3 size={48} color="#D1D5DB" />
            <Text style={styles.emptyText}>No data available</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <View>
            <IsotopeLogo size="medium" />
            <Text style={styles.subtitle}>Track your study patterns</Text>
          </View>
        </View>
      </LinearGradient>

      {/* Period Selector */}
      <View style={styles.periodSection}>
        <View style={styles.periodSelector}>
          {periods.map((period) => (
            <TouchableOpacity
              key={period}
              style={[
                styles.periodButton,
                selectedPeriod === period && styles.periodButtonActive,
              ]}
              onPress={() => setSelectedPeriod(period)}
            >
              <Text
                style={[
                  styles.periodText,
                  selectedPeriod === period && styles.periodTextActive,
                ]}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Stats Overview */}
      <View style={styles.statsSection}>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <View style={styles.statIcon}>
              <Clock size={20} color="#6366F1" />
            </View>
            <Text style={styles.statValue}>{formatTime(totalTime)}</Text>
            <Text style={styles.statLabel}>Total Time</Text>
          </View>
          
          <View style={styles.statCard}>
            <View style={styles.statIcon}>
              <Ionicons name="pulse" size={20} color="#10B981" />
            </View>
            <Text style={styles.statValue}>{totalSessions}</Text>
            <Text style={styles.statLabel}>Sessions</Text>
          </View>

          <View style={styles.statCard}>
            <View style={styles.statIcon}>
              <Ionicons name="trending-up" size={20} color="#F59E0B" />
            </View>
            <Text style={styles.statValue}>{formatTimeShort(averageSession)}</Text>
            <Text style={styles.statLabel}>Avg Session</Text>
          </View>
          
          <View style={styles.statCard}>
            <View style={styles.statIcon}>
              <Target size={20} color="#EF4444" />
            </View>
            <Text style={styles.statValue}>{formatTimeShort(getTotalTimeToday())}</Text>
            <Text style={styles.statLabel}>Today</Text>
          </View>
        </View>
      </View>

      {/* Charts */}
      <View style={styles.chartsSection}>
        {renderBarChart()}
        {renderSubjectChart()}
      </View>

      {/* Insights */}
      <View style={styles.insightsSection}>
        <Text style={styles.sectionTitle}>Insights</Text>
        
        {totalTime > 0 ? (
          <>
            <View style={styles.insightCard}>
              <View style={styles.insightHeader}>
                <Ionicons name="pulse" size={20} color="#10B981" />
                <Text style={styles.insightTitle}>Study Pattern</Text>
              </View>
              <Text style={styles.insightText}>
                You've completed {totalSessions} study sessions this {selectedPeriod}, 
                with an average session length of {formatTime(averageSession)}.
              </Text>
            </View>
            
            {subjectStats.length > 0 && (
              <View style={styles.insightCard}>
                <View style={styles.insightHeader}>
                  <Target size={20} color="#6366F1" />
                  <Text style={styles.insightTitle}>Top Subject</Text>
                </View>
                <Text style={styles.insightText}>
                  You've spent the most time on {subjectStats[0].name} with {formatTime(subjectStats[0].time)} 
                  across {subjectStats[0].sessions} sessions.
                </Text>
              </View>
            )}
            
            <View style={styles.insightCard}>
              <View style={styles.insightHeader}>
                <Ionicons name="trending-up" size={20} color="#F59E0B" />
                <Text style={styles.insightTitle}>Progress</Text>
              </View>
              <Text style={styles.insightText}>
                Keep up the great work! Consistent study sessions lead to better learning outcomes.
              </Text>
            </View>
          </>
        ) : (
          <View style={styles.emptyInsights}>
            <BarChart3 size={48} color="#D1D5DB" />
            <Text style={styles.emptyTitle}>No data yet</Text>
            <Text style={styles.emptyText}>
              Start using the timer to see your study analytics here.
            </Text>
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  periodSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  periodSelector: {
    flexDirection: 'row',
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  periodButton: {
    flex: 1,
    paddingVertical: 10,
    alignItems: 'center',
    borderRadius: 8,
  },
  periodButtonActive: {
    backgroundColor: '#6366F1',
  },
  periodText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  periodTextActive: {
    color: '#FFFFFF',
  },
  statsSection: {
    paddingHorizontal: 24,
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  statCard: {
    flex: 1,
    minWidth: (width - 60) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statIcon: {
    width: 40,
    height: 40,
    backgroundColor: '#F3F4F6',
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  chartsSection: {
    paddingHorizontal: 24,
  },
  chartContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  barChart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 160,
  },
  barContainer: {
    alignItems: 'center',
    flex: 1,
  },
  barWrapper: {
    height: 120,
    justifyContent: 'flex-end',
    marginBottom: 8,
  },
  bar: {
    width: 20,
    borderRadius: 10,
    minHeight: 4,
  },
  barLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginBottom: 2,
  },
  barValue: {
    fontSize: 10,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
  },
  pieChart: {
    flexDirection: 'row',
    width: '100%',
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
    marginBottom: 16,
  },
  pieSlice: {
    height: '100%',
  },
  pieLegend: {
    gap: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  legendColor: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  legendText: {
    flex: 1,
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  legendValue: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  emptyChart: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    marginTop: 8,
  },
  insightsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  insightCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 8,
  },
  insightTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  insightText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
  emptyInsights: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginTop: 16,
    marginBottom: 8,
  },
});