import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  Play,
  Pause,
  Square,
  Volume2,
  Smartphone,
  Clock,
  Zap,
  Coffee,
  Target,
  Shield,
  Settings,
} from 'lucide-react-native';
import { useDistractionBlocking } from '@/hooks/useDistractionBlocking';
import { useRouter } from 'expo-router';
import BlockingNotifications from '@/components/BlockingNotifications';
import DistractionBlockingDemo from '@/components/DistractionBlockingDemo';
import { notificationService } from '@/services/notificationService';


const { width } = Dimensions.get('window');



interface SoundOption {
  id: string;
  name: string;
  icon: string;
  active: boolean;
}

export default function FocusScreen() {
  const router = useRouter();
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [timeLeft, setTimeLeft] = useState(25 * 60); // 25 minutes
  const [breakTime] = useState(5 * 60); // 5 minutes
  const [isBreak, setIsBreak] = useState(false);
  const [currentCycle, setCurrentCycle] = useState(1);
  const [totalCycles] = useState(4);
  const [focusStreak] = useState(12);
  const [showDemo, setShowDemo] = useState(false);

  // Use distraction blocking hook
  const {
    isBlockingEnabled,
    stats,
    recentlyBlockedApps,
    setFocusContext,
    recordDistractionAttempt,
    isLoading: isBlockingLoading,
  } = useDistractionBlocking();

  const [soundOptions, setSoundOptions] = useState<SoundOption[]>([
    { id: '1', name: 'Rain', icon: '🌧️', active: false },
    { id: '2', name: 'Forest', icon: '🌲', active: true },
    { id: '3', name: 'Ocean', icon: '🌊', active: false },
    { id: '4', name: 'Silence', icon: '🔇', active: false },
  ]);

  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (isSessionActive && !isPaused && timeLeft > 0) {
      interval = setInterval(() => {
        setTimeLeft((prev) => prev - 1);
      }, 1000);
    } else if (timeLeft === 0) {
      handleTimerComplete();
    }
    return () => {
      if (interval) {
        clearInterval(interval);
      }
    };
  }, [isSessionActive, isPaused, timeLeft]);

  const handleTimerComplete = async () => {
    if (!isBreak) {
      // Focus session completed
      const sessionDuration = 25 * 60; // 25 minutes
      await notificationService.showFocusCompleteNotification(sessionDuration);

      setIsBreak(true);
      setTimeLeft(breakTime);
      if (currentCycle < totalCycles) {
        // Short break
        setTimeLeft(5 * 60);
        await notificationService.showBreakReminder();
      } else {
        // Long break after 4 cycles
        setTimeLeft(15 * 60);
        await notificationService.showSuccessNotification(
          '🏆 Pomodoro cycle complete! Time for a longer break.'
        );
      }
      // Switch to break context
      setFocusContext('break');
    } else {
      // Break completed
      setIsBreak(false);
      if (currentCycle < totalCycles) {
        setCurrentCycle(prev => prev + 1);
        setTimeLeft(25 * 60);
        // Switch back to focus context
        setFocusContext('focus');
        await notificationService.showSuccessNotification(
          '🎯 Break complete! Ready for your next focus session.'
        );
      } else {
        // All cycles completed
        setIsSessionActive(false);
        setCurrentCycle(1);
        setTimeLeft(25 * 60);
        // Reset to normal context
        setFocusContext('normal');
        await notificationService.showSuccessNotification(
          '🎉 All cycles complete! Great work today!'
        );
      }
    }
  };

  const startSession = () => {
    setIsSessionActive(true);
    setIsPaused(false);
    // Set focus context for distraction blocking
    setFocusContext(isBreak ? 'break' : 'focus');
  };

  const pauseSession = () => {
    setIsPaused(!isPaused);
  };

  const stopSession = () => {
    setIsSessionActive(false);
    setIsPaused(false);
    setIsBreak(false);
    setCurrentCycle(1);
    setTimeLeft(25 * 60);
    // Reset context to normal
    setFocusContext('normal');
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const toggleSound = (soundId: string) => {
    setSoundOptions(prev => 
      prev.map(sound => ({
        ...sound,
        active: sound.id === soundId ? !sound.active : false
      }))
    );
  };

  const getProgressPercentage = () => {
    const totalTime = isBreak ? breakTime : 25 * 60;
    return ((totalTime - timeLeft) / totalTime) * 100;
  };



  return (
    <View style={styles.container}>
      <BlockingNotifications />

      <ScrollView showsVerticalScrollIndicator={false}>
        <LinearGradient
          colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
          style={styles.headerGradient}
        >
          <View style={styles.header}>
            <Text style={styles.title}>Focus Mode</Text>
            <Text style={styles.subtitle}>
              {isBreak ? 'Take a break!' : 'Time to focus'}
            </Text>
          </View>
        </LinearGradient>

      {/* Session Stats */}
      <View style={styles.statsSection}>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Zap size={20} color="#6B46C1" />
            <Text style={styles.statValue}>{focusStreak}</Text>
            <Text style={styles.statLabel}>Focus Streak</Text>
          </View>

          <View style={styles.statCard}>
            <Clock size={20} color="#10B981" />
            <Text style={styles.statValue}>{currentCycle}/{totalCycles}</Text>
            <Text style={styles.statLabel}>Cycle</Text>
          </View>

          <View style={styles.statCard}>
            <Smartphone size={20} color="#EF4444" />
            <Text style={styles.statValue}>{stats.todaysBlockedAttempts}</Text>
            <Text style={styles.statLabel}>Blocked</Text>
          </View>
        </View>
      </View>

      {/* Main Timer */}
      <View style={styles.timerSection}>
        <View style={styles.timerContainer}>
          <LinearGradient
            colors={['rgba(255,255,255,0.1)', 'rgba(255,255,255,0.05)']}
            style={styles.timerCard}
          >
            <View style={styles.timerHeader}>
              <View style={styles.phaseIndicator}>
                <LinearGradient
                  colors={isBreak ? ['#10B981', '#34D399'] : ['#6B46C1', '#7C3AED']}
                  style={styles.phaseGradient}
                >
                  <Text style={styles.phaseIcon}>
                    {isBreak ? '☕' : '🎯'}
                  </Text>
                </LinearGradient>
              </View>
              <View style={styles.timerInfo}>
                <Text style={styles.timerLabel}>
                  {isBreak ? 'Break Time' : 'Focus Time'}
                </Text>
                <Text style={styles.cycleText}>
                  Cycle {currentCycle} of {totalCycles}
                </Text>
              </View>
            </View>

            <View style={styles.timerDisplay}>
              <Text style={styles.timerText}>{formatTime(timeLeft)}</Text>

              <View style={styles.progressContainer}>
                <View style={styles.progressTrack}>
                  <LinearGradient
                    colors={isBreak ? ['#10B981', '#34D399'] : ['#6B46C1', '#7C3AED']}
                    style={[
                      styles.progressFill,
                      { width: `${getProgressPercentage()}%` }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {Math.round(getProgressPercentage())}% Complete
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Timer Controls */}
        <View style={styles.timerControls}>
          {!isSessionActive ? (
            <TouchableOpacity style={styles.startButton} onPress={startSession}>
              <LinearGradient
                colors={['#6B46C1', '#7C3AED']}
                style={styles.startGradient}
              >
                <Play size={32} color="#FFFFFF" />
                <Text style={styles.startText}>Start Focus</Text>
              </LinearGradient>
            </TouchableOpacity>
          ) : (
            <View style={styles.activeControls}>
              <TouchableOpacity style={styles.controlButton} onPress={pauseSession}>
                {isPaused ? (
                  <Play size={28} color="#FFFFFF" />
                ) : (
                  <Pause size={28} color="#FFFFFF" />
                )}
              </TouchableOpacity>
              
              <TouchableOpacity style={styles.stopButton} onPress={stopSession}>
                <Square size={28} color="#FFFFFF" />
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>

      {/* Focus Sounds */}
      <View style={styles.soundsSection}>
        <Text style={styles.sectionTitle}>Focus Sounds</Text>
        <View style={styles.soundGrid}>
          {soundOptions.map((sound) => (
            <TouchableOpacity
              key={sound.id}
              style={[
                styles.soundCard,
                sound.active && styles.soundCardActive,
              ]}
              onPress={() => toggleSound(sound.id)}
            >
              <Text style={styles.soundIcon}>{sound.icon}</Text>
              <Text
                style={[
                  styles.soundName,
                  sound.active && styles.soundNameActive,
                ]}
              >
                {sound.name}
              </Text>
              {sound.active && (
                <View style={styles.activeBadge}>
                  <Volume2 size={16} color="#FFFFFF" />
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Distraction Blocking */}
      <View style={styles.blockingSection}>
        <View style={styles.blockingSectionHeader}>
          <Text style={styles.sectionTitle}>Distraction Blocking</Text>
          <View style={styles.blockingActions}>
            <TouchableOpacity
              style={styles.demoButton}
              onPress={() => setShowDemo(!showDemo)}
            >
              <Text style={styles.demoButtonText}>
                {showDemo ? 'Hide Demo' : 'Test Blocking'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.settingsButton}
              onPress={() => router.push('/distraction-settings')}
            >
              <Settings size={20} color="#6B7280" />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.blockingCard}>
          <View style={styles.blockingHeader}>
            <Shield size={24} color={isBlockingEnabled ? "#10B981" : "#EF4444"} />
            <Text style={styles.blockingTitle}>Active Blocking</Text>
            <View style={[
              styles.statusBadge,
              { backgroundColor: isBlockingEnabled ? "#10B981" : "#EF4444" }
            ]}>
              <Text style={styles.statusText}>
                {isBlockingEnabled ? "ON" : "OFF"}
              </Text>
            </View>
          </View>

          <Text style={styles.blockingDescription}>
            {isBlockingEnabled
              ? "Social media, gaming, and entertainment apps are blocked during focus sessions."
              : "Distraction blocking is currently disabled. Enable it to block distracting apps during focus sessions."
            }
          </Text>

          {isBlockingEnabled && recentlyBlockedApps.length > 0 && (
            <View style={styles.blockedApps}>
              <Text style={styles.blockedTitle}>Recently Blocked:</Text>
              <View style={styles.blockedList}>
                {recentlyBlockedApps.map((app, index) => (
                  <Text key={index} style={styles.blockedApp}>
                    📱 {app.name} ({app.attempts} attempt{app.attempts !== 1 ? 's' : ''})
                  </Text>
                ))}
              </View>
            </View>
          )}

          {isBlockingEnabled && (
            <View style={styles.blockingStats}>
              <View style={styles.statRow}>
                <Text style={styles.statRowLabel}>Effectiveness:</Text>
                <Text style={styles.statRowValue}>{stats.blockingEffectiveness}%</Text>
              </View>
              <View style={styles.statRow}>
                <Text style={styles.statRowLabel}>Streak:</Text>
                <Text style={styles.statRowValue}>{stats.streakDays} days</Text>
              </View>
            </View>
          )}
        </View>
      </View>

      {/* Break Activities */}
      {isBreak && (
        <View style={styles.breakSection}>
          <Text style={styles.sectionTitle}>Break Activities</Text>
          
          <View style={styles.activityGrid}>
            <TouchableOpacity style={styles.activityCard}>
              <Coffee size={24} color="#F59E0B" />
              <Text style={styles.activityText}>Get a drink</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.activityCard}>
              <Target size={24} color="#10B981" />
              <Text style={styles.activityText}>Stretch</Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      {/* Quick Tips */}
      <View style={styles.tipsSection}>
        <Text style={styles.sectionTitle}>Focus Tips</Text>
        
        <View style={styles.tipCard}>
          <Text style={styles.tipTitle}>💡 Stay Hydrated</Text>
          <Text style={styles.tipText}>
            Keep water nearby to maintain focus and avoid dehydration breaks.
          </Text>
        </View>
        
        <View style={styles.tipCard}>
          <Text style={styles.tipTitle}>🧘 Deep Breathing</Text>
          <Text style={styles.tipText}>
            Take 3 deep breaths before starting to center your mind.
          </Text>
        </View>
      </View>

      {/* Demo Section */}
      {isBlockingEnabled && (
        <DistractionBlockingDemo isVisible={showDemo} />
      )}

    </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  statsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginVertical: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  timerSection: {
    alignItems: 'center',
    marginVertical: 24,
    paddingHorizontal: 20,
  },
  timerContainer: {
    marginBottom: 32,
    width: '100%',
  },
  timerCard: {
    borderRadius: 32,
    padding: 32,
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 8,
  },
  timerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 24,
    gap: 16,
  },
  phaseIndicator: {
    borderRadius: 16,
    overflow: 'hidden',
  },
  phaseGradient: {
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
  },
  phaseIcon: {
    fontSize: 24,
  },
  timerInfo: {
    flex: 1,
  },
  timerLabel: {
    fontSize: 18,
    fontFamily: 'Inter-Semibold',
    color: '#1F2937',
    marginBottom: 4,
  },
  cycleText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  timerDisplay: {
    alignItems: 'center',
  },
  timerText: {
    fontSize: 64,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    textAlign: 'center',
    marginBottom: 24,
    letterSpacing: -2,
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    gap: 12,
  },
  progressTrack: {
    width: '100%',
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  timerControls: {
    alignItems: 'center',
    marginTop: 8,
  },
  startButton: {
    borderRadius: 20,
    overflow: 'hidden',
  },
  startGradient: {
    paddingVertical: 16,
    paddingHorizontal: 32,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  startText: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#FFFFFF',
  },
  activeControls: {
    flexDirection: 'row',
    gap: 20,
  },
  controlButton: {
    width: 64,
    height: 64,
    backgroundColor: '#6B46C1',
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  stopButton: {
    width: 64,
    height: 64,
    backgroundColor: '#EF4444',
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
  },
  soundsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  soundGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  soundCard: {
    flex: 1,
    minWidth: (width - 72) / 2,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  soundCardActive: {
    borderColor: '#6B46C1',
    backgroundColor: '#F3F4F6',
  },
  soundIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  soundName: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  soundNameActive: {
    color: '#6B46C1',
  },
  activeBadge: {
    position: 'absolute',
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    backgroundColor: '#6B46C1',
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  blockingSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  blockingSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  settingsButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6',
  },
  blockingCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  blockingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  blockingTitle: {
    flex: 1,
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  statusBadge: {
    backgroundColor: '#10B981',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  statusText: {
    fontSize: 12,
    fontFamily: 'Inter-Bold',
    color: '#FFFFFF',
  },
  blockingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 16,
  },
  blockedApps: {
    backgroundColor: '#FEF2F2',
    borderRadius: 12,
    padding: 12,
  },
  blockedTitle: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#DC2626',
    marginBottom: 8,
  },
  blockedList: {
    gap: 4,
  },
  blockedApp: {
    fontSize: 13,
    fontFamily: 'Inter-Regular',
    color: '#7F1D1D',
  },
  blockingStats: {
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  statRowLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  statRowValue: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  blockingActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  demoButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#6B46C1',
    borderRadius: 8,
  },
  demoButtonText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
  breakSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  activityGrid: {
    flexDirection: 'row',
    gap: 16,
  },
  activityCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    gap: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activityText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#1F2937',
  },
  tipsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
    marginBottom: 32,
  },
  tipCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  tipTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 8,
  },
  tipText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
  },
});