import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Modal,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Plus, Target, Calendar, Edit3, Trash2, X } from '@/components/Icons';
import { Ionicons } from '@expo/vector-icons';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
} from 'react-native-reanimated';
import IsotopeLogo from '@/components/IsotopeLogo';
import SubjectPicker from '@/components/SubjectPicker';
import { useGoals } from '@/hooks/useGoals';
import { useSubjects } from '@/hooks/useSubjects';
import { Goal } from '@/types/app';

export default function GoalsScreen() {
  const { goals, addGoal, updateGoal, deleteGoal, toggleGoalCompletion, getUpcomingGoals, getOverdueGoals } = useGoals();
  const { getSubjectById } = useSubjects();
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingGoal, setEditingGoal] = useState<Goal | null>(null);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [targetDate, setTargetDate] = useState('');
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');
  const [selectedSubject, setSelectedSubject] = useState<any>(null);

  const modalScale = useSharedValue(0);

  const openAddModal = () => {
    setEditingGoal(null);
    setTitle('');
    setDescription('');
    setTargetDate('');
    setPriority('medium');
    setSelectedSubject(null);
    setShowAddModal(true);
    modalScale.value = withSpring(1);
  };

  const openEditModal = (goal: Goal) => {
    setEditingGoal(goal);
    setTitle(goal.title);
    setDescription(goal.description);
    setTargetDate(goal.targetDate.toISOString().split('T')[0]);
    setPriority(goal.priority);
    setSelectedSubject(goal.subjectId ? getSubjectById(goal.subjectId) : null);
    setShowAddModal(true);
    modalScale.value = withSpring(1);
  };

  const closeModal = () => {
    modalScale.value = withSpring(0, {}, () => {
      setShowAddModal(false);
    });
  };

  const handleSave = () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a goal title');
      return;
    }

    if (!targetDate) {
      Alert.alert('Error', 'Please select a target date');
      return;
    }

    const goalData = {
      title: title.trim(),
      description: description.trim(),
      targetDate: new Date(targetDate),
      priority,
      subjectId: selectedSubject?.id,
      completed: false,
    };

    if (editingGoal) {
      updateGoal(editingGoal.id, goalData);
    } else {
      addGoal(goalData);
    }

    closeModal();
  };

  const handleDelete = (goal: Goal) => {
    Alert.alert(
      'Delete Goal',
      `Are you sure you want to delete "${goal.title}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => deleteGoal(goal.id),
        },
      ]
    );
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return '#EF4444';
      case 'medium': return '#F59E0B';
      case 'low': return '#10B981';
      default: return '#6B7280';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'high': return '🔴';
      case 'medium': return '🟡';
      case 'low': return '🟢';
      default: return '⚪';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const isOverdue = (date: Date) => {
    return date < new Date() && date.toDateString() !== new Date().toDateString();
  };

  const modalAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: modalScale.value }],
  }));

  const activeGoals = goals.filter(goal => !goal.completed);
  const completedGoals = goals.filter(goal => goal.completed);
  const upcomingGoals = getUpcomingGoals();
  const overdueGoals = getOverdueGoals();

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={['#F8FAFC', '#EEF2FF', '#F8FAFC']}
        style={styles.headerGradient}
      >
        <View style={styles.header}>
          <View>
            <IsotopeLogo size="medium" />
            <Text style={styles.subtitle}>Set and track your goals</Text>
          </View>
          <TouchableOpacity style={styles.addButton} onPress={openAddModal}>
            <Plus size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Stats Overview */}
      <View style={styles.statsSection}>
        <View style={styles.statsGrid}>
          <View style={styles.statCard}>
            <Target size={20} color="#6366F1" />
            <Text style={styles.statValue}>{activeGoals.length}</Text>
            <Text style={styles.statLabel}>Active Goals</Text>
          </View>
          
          <View style={styles.statCard}>
            <Ionicons name="checkmark-circle" size={20} color="#10B981" />
            <Text style={styles.statValue}>{completedGoals.length}</Text>
            <Text style={styles.statLabel}>Completed</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="alert-circle" size={20} color="#EF4444" />
            <Text style={styles.statValue}>{overdueGoals.length}</Text>
            <Text style={styles.statLabel}>Overdue</Text>
          </View>
        </View>
      </View>

      {/* Overdue Goals */}
      {overdueGoals.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>⚠️ Overdue Goals</Text>
          {overdueGoals.map((goal) => (
            <View key={goal.id} style={[styles.goalCard, styles.overdueCard]}>
              <View style={styles.goalHeader}>
                <TouchableOpacity onPress={() => toggleGoalCompletion(goal.id)}>
                  <Ionicons name="ellipse-outline" size={20} color="#EF4444" />
                </TouchableOpacity>
                <View style={styles.goalInfo}>
                  <Text style={styles.goalTitle}>{goal.title}</Text>
                  {goal.description && (
                    <Text style={styles.goalDescription}>{goal.description}</Text>
                  )}
                </View>
                <TouchableOpacity onPress={() => openEditModal(goal)}>
                  <Edit3 size={16} color="#6B7280" />
                </TouchableOpacity>
              </View>
              
              <View style={styles.goalMeta}>
                <View style={styles.goalDate}>
                  <Calendar size={14} color="#EF4444" />
                  <Text style={[styles.goalDateText, { color: '#EF4444' }]}>
                    {formatDate(goal.targetDate)}
                  </Text>
                </View>
                
                <View style={styles.goalPriority}>
                  <Text style={styles.priorityEmoji}>{getPriorityIcon(goal.priority)}</Text>
                  <Text style={styles.priorityText}>{goal.priority}</Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      )}

      {/* Active Goals */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>🎯 Active Goals</Text>
        {activeGoals.filter(goal => !isOverdue(goal.targetDate)).length === 0 ? (
          <View style={styles.emptyState}>
            <Target size={48} color="#D1D5DB" />
            <Text style={styles.emptyTitle}>No active goals</Text>
            <Text style={styles.emptyText}>Create your first goal to get started!</Text>
          </View>
        ) : (
          activeGoals
            .filter(goal => !isOverdue(goal.targetDate))
            .sort((a, b) => a.targetDate.getTime() - b.targetDate.getTime())
            .map((goal) => {
              const subject = goal.subjectId ? getSubjectById(goal.subjectId) : null;
              
              return (
                <View key={goal.id} style={styles.goalCard}>
                  <View style={styles.goalHeader}>
                    <TouchableOpacity onPress={() => toggleGoalCompletion(goal.id)}>
                      <Ionicons name="ellipse-outline" size={20} color="#6366F1" />
                    </TouchableOpacity>
                    <View style={styles.goalInfo}>
                      <Text style={styles.goalTitle}>{goal.title}</Text>
                      {goal.description && (
                        <Text style={styles.goalDescription}>{goal.description}</Text>
                      )}
                      {subject && (
                        <View style={styles.subjectTag}>
                          <View style={[styles.subjectDot, { backgroundColor: subject.color }]} />
                          <Text style={styles.subjectName}>{subject.name}</Text>
                        </View>
                      )}
                    </View>
                    <TouchableOpacity onPress={() => openEditModal(goal)}>
                      <Edit3 size={16} color="#6B7280" />
                    </TouchableOpacity>
                  </View>
                  
                  <View style={styles.goalMeta}>
                    <View style={styles.goalDate}>
                      <Calendar size={14} color="#6B7280" />
                      <Text style={styles.goalDateText}>{formatDate(goal.targetDate)}</Text>
                    </View>
                    
                    <View style={styles.goalPriority}>
                      <Text style={styles.priorityEmoji}>{getPriorityIcon(goal.priority)}</Text>
                      <Text style={styles.priorityText}>{goal.priority}</Text>
                    </View>
                  </View>
                </View>
              );
            })
        )}
      </View>

      {/* Completed Goals */}
      {completedGoals.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>✅ Completed Goals</Text>
          {completedGoals.map((goal) => {
            const subject = goal.subjectId ? getSubjectById(goal.subjectId) : null;
            
            return (
              <View key={goal.id} style={[styles.goalCard, styles.completedCard]}>
                <View style={styles.goalHeader}>
                  <TouchableOpacity onPress={() => toggleGoalCompletion(goal.id)}>
                    <Ionicons name="checkmark-circle" size={20} color="#10B981" />
                  </TouchableOpacity>
                  <View style={styles.goalInfo}>
                    <Text style={[styles.goalTitle, styles.completedTitle]}>{goal.title}</Text>
                    {goal.description && (
                      <Text style={[styles.goalDescription, styles.completedDescription]}>
                        {goal.description}
                      </Text>
                    )}
                    {subject && (
                      <View style={styles.subjectTag}>
                        <View style={[styles.subjectDot, { backgroundColor: subject.color }]} />
                        <Text style={styles.subjectName}>{subject.name}</Text>
                      </View>
                    )}
                  </View>
                  <TouchableOpacity onPress={() => handleDelete(goal)}>
                    <Trash2 size={16} color="#EF4444" />
                  </TouchableOpacity>
                </View>
              </View>
            );
          })}
        </View>
      )}

      {/* Add/Edit Goal Modal */}
      <Modal visible={showAddModal} transparent animationType="fade">
        <View style={styles.modalOverlay}>
          <Animated.View style={[styles.modalContent, modalAnimatedStyle]}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>
                {editingGoal ? 'Edit Goal' : 'Add New Goal'}
              </Text>
              <TouchableOpacity onPress={closeModal}>
                <X size={24} color="#6B7280" />
              </TouchableOpacity>
            </View>
            
            <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Goal Title *</Text>
                <TextInput
                  style={styles.textInput}
                  value={title}
                  onChangeText={setTitle}
                  placeholder="Enter goal title"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
              
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Description</Text>
                <TextInput
                  style={[styles.textInput, styles.textArea]}
                  value={description}
                  onChangeText={setDescription}
                  placeholder="Enter goal description"
                  placeholderTextColor="#9CA3AF"
                  multiline
                  numberOfLines={3}
                />
              </View>
              
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Target Date *</Text>
                <TextInput
                  style={styles.textInput}
                  value={targetDate}
                  onChangeText={setTargetDate}
                  placeholder="YYYY-MM-DD"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
              
              <View style={styles.inputGroup}>
                <Text style={styles.inputLabel}>Priority</Text>
                <View style={styles.prioritySelector}>
                  {(['low', 'medium', 'high'] as const).map((p) => (
                    <TouchableOpacity
                      key={p}
                      style={[
                        styles.priorityButton,
                        priority === p && styles.priorityButtonActive,
                        { borderColor: getPriorityColor(p) },
                      ]}
                      onPress={() => setPriority(p)}
                    >
                      <Text style={styles.priorityEmoji}>{getPriorityIcon(p)}</Text>
                      <Text
                        style={[
                          styles.priorityButtonText,
                          priority === p && { color: getPriorityColor(p) },
                        ]}
                      >
                        {p.charAt(0).toUpperCase() + p.slice(1)}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>
              
              <View style={styles.inputGroup}>
                <SubjectPicker
                  selectedSubject={selectedSubject}
                  onSelectSubject={setSelectedSubject}
                />
              </View>
            </ScrollView>
            
            <View style={styles.modalActions}>
              {editingGoal && (
                <TouchableOpacity
                  style={styles.deleteButton}
                  onPress={() => {
                    closeModal();
                    handleDelete(editingGoal);
                  }}
                >
                  <Trash2 size={16} color="#EF4444" />
                  <Text style={styles.deleteText}>Delete</Text>
                </TouchableOpacity>
              )}
              
              <View style={styles.actionButtons}>
                <TouchableOpacity style={styles.cancelButton} onPress={closeModal}>
                  <Text style={styles.cancelText}>Cancel</Text>
                </TouchableOpacity>
                
                <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
                  <LinearGradient
                    colors={['#6366F1', '#8B5CF6']}
                    style={styles.saveGradient}
                  >
                    <Text style={styles.saveText}>
                      {editingGoal ? 'Update' : 'Create'}
                    </Text>
                  </LinearGradient>
                </TouchableOpacity>
              </View>
            </View>
          </Animated.View>
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  headerGradient: {
    paddingTop: 60,
    paddingBottom: 20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  subtitle: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    marginTop: 4,
  },
  addButton: {
    width: 44,
    height: 44,
    backgroundColor: '#6366F1',
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  statsSection: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#1F2937',
    marginVertical: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 24,
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 16,
  },
  goalCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  overdueCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#EF4444',
    backgroundColor: '#FEF2F2',
  },
  completedCard: {
    opacity: 0.7,
  },
  goalHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    marginBottom: 12,
  },
  goalInfo: {
    flex: 1,
  },
  goalTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
    marginBottom: 4,
  },
  completedTitle: {
    textDecorationLine: 'line-through',
    color: '#6B7280',
  },
  goalDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
    lineHeight: 20,
    marginBottom: 8,
  },
  completedDescription: {
    textDecorationLine: 'line-through',
  },
  subjectTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginTop: 4,
  },
  subjectDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  subjectName: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  goalMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  goalDate: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  goalDateText: {
    fontSize: 12,
    fontFamily: 'Inter-Regular',
    color: '#6B7280',
  },
  goalPriority: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  priorityEmoji: {
    fontSize: 12,
  },
  priorityText: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
    textTransform: 'capitalize',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 40,
  },
  emptyTitle: {
    fontSize: 18,
    fontFamily: 'Inter-SemiBold',
    color: '#6B7280',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9CA3AF',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    width: '100%',
    maxWidth: 400,
    maxHeight: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 20,
    fontFamily: 'Inter-SemiBold',
    color: '#1F2937',
  },
  modalBody: {
    padding: 20,
    maxHeight: 400,
  },
  inputGroup: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#374151',
    marginBottom: 8,
  },
  textInput: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    fontFamily: 'Inter-Regular',
    color: '#1F2937',
  },
  textArea: {
    height: 80,
    textAlignVertical: 'top',
  },
  prioritySelector: {
    flexDirection: 'row',
    gap: 8,
  },
  priorityButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    gap: 6,
  },
  priorityButtonActive: {
    backgroundColor: '#F3F4F6',
  },
  priorityButtonText: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  modalActions: {
    padding: 20,
    gap: 16,
  },
  deleteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    paddingVertical: 12,
    backgroundColor: '#FEF2F2',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  deleteText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#EF4444',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 14,
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    alignItems: 'center',
  },
  cancelText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6B7280',
  },
  saveButton: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  saveGradient: {
    paddingVertical: 14,
    alignItems: 'center',
  },
  saveText: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#FFFFFF',
  },
});